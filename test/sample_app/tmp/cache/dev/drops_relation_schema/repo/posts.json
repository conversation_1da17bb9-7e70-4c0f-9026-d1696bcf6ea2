{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "primary_key": true, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"], "ecto_type": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "title"], "type": ["atom", "string"], "source": ["atom", "title"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "body"], "type": ["atom", "string"], "source": ["atom", "body"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "published"], "type": ["atom", "integer"], "source": ["atom", "published"], "ecto_type": ["atom", "integer"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": 0, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "view_count"], "type": ["atom", "integer"], "source": ["atom", "view_count"], "ecto_type": ["atom", "integer"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": true, "check_constraints": [], "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"], "ecto_type": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}], "source": "posts", "primary_key": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "primary_key": true, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"], "ecto_type": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "user_id"], "association_name": null, "references_field": ["atom", "id"], "references_table": "users"}, "__struct__": "ForeignKey"}], "indices": {"attributes": {"indices": [{"attributes": {"name": "posts_title_index", "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {}, "name": ["atom", "title"], "type": ["atom", "unknown"], "source": ["atom", "title"], "ecto_type": ["atom", "unknown"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": "posts_published_index", "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {}, "name": ["atom", "published"], "type": ["atom", "unknown"], "source": ["atom", "published"], "ecto_type": ["atom", "unknown"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": "posts_user_id_index", "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {}, "name": ["atom", "user_id"], "type": ["atom", "unknown"], "source": ["atom", "user_id"], "ecto_type": ["atom", "unknown"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}