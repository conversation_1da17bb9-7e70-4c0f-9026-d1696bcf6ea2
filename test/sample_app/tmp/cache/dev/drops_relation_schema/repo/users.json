{"digest": "B1989009233E0EA9AECF040845FE0F6B2D0C80B919361A87C51937B74F4F2165", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "primary_key": true, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"], "ecto_type": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "email"], "type": ["atom", "string"], "source": ["atom", "email"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "first_name"], "type": ["atom", "string"], "source": ["atom", "first_name"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "last_name"], "type": ["atom", "string"], "source": ["atom", "last_name"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "age"], "type": ["atom", "integer"], "source": ["atom", "age"], "ecto_type": ["atom", "integer"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": true, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "is_active"], "type": ["atom", "integer"], "source": ["atom", "is_active"], "ecto_type": ["atom", "integer"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "profile_data"], "type": ["atom", "string"], "source": ["atom", "profile_data"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": "[]", "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "tags"], "type": ["atom", "string"], "source": ["atom", "tags"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "score"], "type": ["atom", "decimal"], "source": ["atom", "score"], "ecto_type": ["atom", "decimal"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "birth_date"], "type": ["atom", "string"], "source": ["atom", "birth_date"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "last_login_at"], "type": ["atom", "string"], "source": ["atom", "last_login_at"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "primary_key": false, "foreign_key": false, "check_constraints": [], "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"], "ecto_type": ["atom", "string"]}, "__struct__": "Field"}], "source": "users", "primary_key": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "primary_key": true, "foreign_key": false, "check_constraints": [], "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"], "ecto_type": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [], "indices": {"attributes": {"indices": [{"attributes": {"name": "users_is_active_index", "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {}, "name": ["atom", "is_active"], "type": ["atom", "unknown"], "source": ["atom", "is_active"], "ecto_type": ["atom", "unknown"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": "users_last_name_first_name_index", "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {}, "name": ["atom", "last_name"], "type": ["atom", "unknown"], "source": ["atom", "last_name"], "ecto_type": ["atom", "unknown"]}, "__struct__": "Field"}, {"attributes": {"meta": {}, "name": ["atom", "first_name"], "type": ["atom", "unknown"], "source": ["atom", "first_name"], "ecto_type": ["atom", "unknown"]}, "__struct__": "Field"}], "unique": false}, "__struct__": "Index"}, {"attributes": {"name": "users_email_index", "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {}, "name": ["atom", "email"], "type": ["atom", "unknown"], "source": ["atom", "email"], "ecto_type": ["atom", "unknown"]}, "__struct__": "Field"}], "unique": true}, "__struct__": "Index"}]}, "__struct__": "Indices"}}, "__struct__": "<PERSON><PERSON><PERSON>"}}